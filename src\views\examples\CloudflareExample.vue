<template>
  <div class="cloudflare-example">
    <div class="header">
      <h1>🔐 Cloudflare 验证示例</h1>
      <p>展示如何在实际项目中使用 Cloudflare 验证组件</p>
    </div>

    <div class="example-section">
      <h2>📱 API 方式调用</h2>
      <p>推荐使用方式，简单快捷</p>

      <div class="button-grid">
        <van-button type="primary" @click="handleLoginClick" :loading="isLoading">
          登录验证
        </van-button>

        <van-button type="success" @click="handleRegisterClick" :loading="isLoading">
          注册验证
        </van-button>

        <van-button type="warning" @click="handleKYCClick" :loading="isLoading">
          KYC验证
        </van-button>

        <van-button type="danger" @click="handleWithdrawalClick" :loading="isLoading">
          提款验证
        </van-button>
      </div>
    </div>

    <div class="example-section">
      <h2>🧩 组件方式调用</h2>
      <p>适合需要更多自定义控制的场景</p>

      <van-button type="primary" @click="showCustomDialog = true" block>
        显示自定义验证弹窗
      </van-button>
    </div>

    <div class="example-section">
      <h2>📋 验证结果</h2>
      <div class="result-display">
        <div
          v-if="lastResult"
          class="result-item"
          :class="lastResult.success ? 'success' : 'error'"
        >
          <h3>{{ lastResult.success ? "✅ 验证成功" : "❌ 验证失败" }}</h3>
          <div class="result-details">
            <p><strong>类型:</strong> {{ lastResult.cfType }}</p>
            <p v-if="lastResult.token">
              <strong>Token:</strong> {{ lastResult.token.substring(0, 20) }}...
            </p>
            <p v-if="lastResult.error"><strong>错误:</strong> {{ lastResult.error }}</p>
            <p v-if="lastResult.cancelled"><strong>状态:</strong> 用户取消</p>
          </div>
        </div>
        <div v-else class="no-result">暂无验证结果</div>
      </div>
    </div>

    <!-- 自定义验证弹窗 -->
    <CloudflareVerifyDialog
      v-model="showCustomDialog"
      :cf-type="CF_TURNSTILE_TYPE.LOGIN_SUBMIT"
      title="tips"
      description="这是一个自定义的验证弹窗示例，展示了组件的灵活性"
      theme="light"
      size="normal"
      appearance="always"
      :auto-close-delay="3000"
      @success="handleVerifySuccess"
      @error="handleVerifyError"
      @cancel="handleVerifyCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { showToast } from "vant";
import {
  verifyLogin,
  verifyRegister,
  verifyKYC,
  verifyWithdrawal,
  CF_TURNSTILE_TYPE,
  type VerifyResult,
} from "@/utils/CloudflareVerifyAPI";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

// 响应式状态
const isLoading = ref(false);
const showCustomDialog = ref(false);
const lastResult = ref<VerifyResult | null>(null);

// API 方式验证处理
const handleApiVerify = async (verifyFn: () => Promise<VerifyResult>, actionName: string) => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    const result = await verifyFn();
    lastResult.value = result;

    if (result.success) {
      showToast(`${actionName}验证成功`);
      console.log(`${actionName}验证成功:`, result);
    } else if (result.cancelled) {
      showToast("验证已取消");
    } else {
      showToast(`${actionName}验证失败`);
      console.error(`${actionName}验证失败:`, result);
    }
  } catch (error) {
    console.error(`${actionName}验证异常:`, error);
    showToast("验证过程中发生错误");
  } finally {
    isLoading.value = false;
  }
};

// 各种验证方法
const handleLoginClick = () => handleApiVerify(verifyLogin, "登录");
const handleRegisterClick = () => handleApiVerify(verifyRegister, "注册");
const handleKYCClick = () => handleApiVerify(verifyKYC, "KYC");
const handleWithdrawalClick = () => handleApiVerify(verifyWithdrawal, "提款");

// 组件方式验证处理
const handleVerifySuccess = (result: any) => {
  lastResult.value = {
    success: true,
    token: result.token,
    cfType: result.cfType,
  };
  showToast("自定义验证成功");
  console.log("自定义验证成功:", result);
  showCustomDialog.value = false;
};

const handleVerifyError = (error: string) => {
  lastResult.value = {
    success: false,
    cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
    error,
  };
  showToast("自定义验证失败");
  console.error("自定义验证失败:", error);
};

const handleVerifyCancel = () => {
  lastResult.value = {
    success: false,
    cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
    cancelled: true,
  };
  showToast("验证已取消");
  console.log("用户取消了自定义验证");
  showCustomDialog.value = false;
};
</script>

<style scoped lang="scss">
.cloudflare-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 32px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

.example-section {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 14px;
    margin-bottom: 16px;
  }
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.result-display {
  .result-item {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid;

    &.success {
      background: #f0f9ff;
      border-color: #bae6fd;
      color: #0369a1;
    }

    &.error {
      background: #fef2f2;
      border-color: #fecaca;
      color: #dc2626;
    }

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .result-details {
      p {
        margin: 4px 0;
        font-size: 14px;

        strong {
          font-weight: 600;
        }
      }
    }
  }

  .no-result {
    padding: 32px;
    text-align: center;
    color: #999;
    font-size: 14px;
    background: #f5f5f5;
    border-radius: 8px;
  }
}

@media (max-width: 768px) {
  .cloudflare-example {
    padding: 16px;
  }

  .button-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .example-section {
    padding: 16px;
  }
}
</style>
