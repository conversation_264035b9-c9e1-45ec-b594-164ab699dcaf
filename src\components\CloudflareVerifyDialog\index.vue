<template>
  <van-dialog
    v-model:show="visible"
    :title="title"
    :close-on-click-overlay="false"
    :close-on-popstate="false"
    :show-cancel-button="showCancelButton"
    :show-confirm-button="false"
    :width="360"
    class="cloudflare-verify-dialog"
    @cancel="handleCancel"
  >
    <div class="dialog-content">
      <!-- 描述文本 -->
      <div v-if="description" class="description">
        {{ description }}
      </div>

      <!-- Turnstile 容器 -->
      <div :id="containerId" class="turnstile-container" :class="{ loading: isLoading }">
        <div v-if="isLoading" class="loading-spinner">
          <van-loading type="spinner" size="24px" />
          <span class="loading-text">Loading verification...</span>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessage" class="error-message">
        <van-icon name="warning-o" />
        <span>{{ errorMessage }}</span>
        <van-button type="primary" size="small" @click="retry" :loading="isLoading">
          Retry
        </van-button>
      </div>

      <!-- 成功信息 -->
      <div v-if="isSuccess" class="success-message">
        <van-icon name="success" color="#07c160" />
        <span>Verification successful!</span>
      </div>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { showToast } from "vant";
import {
  CloudflareMgrNew,
  CF_TURNSTILE_TYPE,
  type TurnstileResult,
  type VerifyCallback,
} from "@/utils/CloudflareMgrNew";

interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue: boolean;
  /** 弹窗标题 */
  title?: string;
  /** 描述文本 */
  description?: string;
  /** 验证类型 */
  cfType: CF_TURNSTILE_TYPE;
  /** 是否显示取消按钮 */
  showCancelButton?: boolean;
  /** 自定义 Site Key */
  siteKey?: string;
  /** 主题 */
  theme?: "light" | "dark" | "auto";
  /** 尺寸 */
  size?: "normal" | "compact";
  /** 外观 */
  appearance?: "always" | "execute" | "interaction-only";
  /** 验证成功后自动关闭延迟时间(ms) */
  autoCloseDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  title: "Security Verification",
  description: "Please complete the security verification to continue.",
  showCancelButton: true,
  theme: "light",
  size: "normal",
  appearance: "always",
  autoCloseDelay: 2000,
});

const emit = defineEmits<{
  /** 更新v-model绑定值 */
  (e: "update:modelValue", value: boolean): void;
  /** 验证成功事件 */
  (e: "success", result: TurnstileResult): void;
  /** 验证失败事件 */
  (e: "error", error: string): void;
  /** 取消事件 */
  (e: "cancel"): void;
}>();

// 响应式状态
const visible = ref(props.modelValue);
const isLoading = ref(false);
const isSuccess = ref(false);
const errorMessage = ref("");
const widgetId = ref<string | null>(null);

// 生成唯一的容器ID
const containerId = computed(
  () => `turnstile-container-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
);

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      initVerification();
    } else {
      cleanup();
    }
  }
);

// 监听visible变化，同步到父组件
watch(visible, (val) => {
  if (val !== props.modelValue) {
    emit("update:modelValue", val);
  }
});

/**
 * 初始化验证
 */
const initVerification = async () => {
  console.log("🚀 Initializing Cloudflare verification");

  // 重置状态
  isLoading.value = true;
  isSuccess.value = false;
  errorMessage.value = "";

  try {
    // 等待下一个tick，确保DOM已渲染
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 渲染Turnstile组件
    const mgr = CloudflareMgrNew.instance;
    widgetId.value = await mgr.renderTurnstile(
      containerId.value,
      props.cfType,
      handleVerificationResult,
      {
        siteKey: props.siteKey,
        theme: props.theme,
        size: props.size,
        appearance: props.appearance,
      }
    );

    isLoading.value = false;
  } catch (error) {
    console.error("❌ Failed to initialize verification:", error);
    isLoading.value = false;
    errorMessage.value = error instanceof Error ? error.message : "Failed to load verification";
    emit("error", errorMessage.value);
  }
};

/**
 * 处理验证结果
 */
const handleVerificationResult: VerifyCallback = (result) => {
  console.log("📨 Verification result:", result);

  if (result === false) {
    errorMessage.value = "Verification failed";
    emit("error", errorMessage.value);
    return;
  }

  if (result.success) {
    isSuccess.value = true;
    errorMessage.value = "";
    emit("success", result);

    // 自动关闭弹窗
    if (props.autoCloseDelay > 0) {
      setTimeout(() => {
        handleClose();
      }, props.autoCloseDelay);
    }
  } else {
    errorMessage.value = result.error || "Verification failed";
    emit("error", errorMessage.value);
  }
};

/**
 * 重试验证
 */
const retry = () => {
  console.log("🔄 Retrying verification");
  cleanup();
  initVerification();
};

/**
 * 取消验证
 */
const handleCancel = () => {
  console.log("❌ Verification cancelled");
  emit("cancel");
  handleClose();
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  visible.value = false;
  emit("update:modelValue", false);
};

/**
 * 清理资源
 */
const cleanup = () => {
  if (widgetId.value) {
    try {
      CloudflareMgrNew.instance.remove(widgetId.value);
    } catch (error) {
      console.warn("⚠️ Failed to remove Turnstile widget:", error);
    }
    widgetId.value = null;
  }

  isLoading.value = false;
  isSuccess.value = false;
  errorMessage.value = "";
};

// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue) {
    initVerification();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped lang="scss">
.cloudflare-verify-dialog {
  :deep(.van-dialog) {
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.van-dialog__header) {
    padding: 20px 20px 0;
    font-weight: 600;
    font-size: 18px;
  }

  :deep(.van-dialog__content) {
    padding: 0;
  }

  :deep(.van-dialog__footer) {
    padding: 16px 20px 20px;
    border-top: 1px solid #ebedf0;
  }
}

.dialog-content {
  padding: 20px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.description {
  color: #646566;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

.turnstile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
  border: 2px dashed #e5e5e5;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;

  &.loading {
    background: #f0f0f0;
  }
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #969799;

  .loading-text {
    font-size: 14px;
  }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;

  .van-icon {
    flex-shrink: 0;
  }

  span {
    flex: 1;
  }
}

.success-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #07c160;
  font-size: 14px;
  justify-content: center;

  .van-icon {
    flex-shrink: 0;
  }
}
</style>
